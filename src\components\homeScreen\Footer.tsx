import FooterSVG from '@/assets/home/<USER>'
import SquareIconButton from '@/components/common/SquareIconButton'
import MediumOrangeButton from '@/assets/home/<USER>'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import { useGameStore } from '@/stores/useGameStore'
import { useForfeitSpin } from '@/hooks/useForfeitSpin'
import { useGameCampaign } from '@/hooks/useGameCampaign'

interface FooterProps {
  onStageClick: (stage: number) => void
  onOpenDailyMission: () => void
  onOpenLeaderboard: () => void
  onOpenHistory: () => void
  onOpenGuideline: () => void
}

export const Footer: React.FC<FooterProps> = ({
  onStageClick,
  onOpenDailyMission,
  onOpenLeaderboard,
  onOpenHistory,
  onOpenGuideline
}) => {
  const { currentLevel, gameCampaignId, setGameCampaign } = useGameStore()
  const { mutateAsync: forfeitSpin } = useForfeitSpin()
  const { mutateAsync: refreshGameCampaign } = useGameCampaign()

  const handlePlayNow = async () => {
    try {
      if (gameCampaignId) {
        await forfeitSpin({
          action: 'FORFEIT_SPIN',
          levelNumber: currentLevel,
          gameCampaignId,
          userId: 'x391d6b4ecadb6625af716fe675cc21cf'
        })

        // Refresh game campaign data before navigating to game
        const data = await refreshGameCampaign({
          userId: 'x391d6b4ecadb6625af716fe675cc21cf',
          appVersion: '1.0.0',
          from: 'web'
        })
        setGameCampaign(data)
      }
      onStageClick(currentLevel)
    } catch (error) {
      console.error('Failed to forfeit spin or refresh game campaign:', error)
      // Still navigate to game even if API call fails
      onStageClick(currentLevel)
    }
  }
  return (
    <div className='absolute bottom-0 left-0 z-20 h-[18.1%] w-full'>
      <div className='relative h-full w-full'>
        {/* Background */}
        <div className='absolute bottom-0 left-0 h-[88.4%] w-full'>
          <FooterSVG className='drop-shadow-up-2xl h-full w-full' />
        </div>

        {/* Content: stack orange button + 4 buttons */}
        <div className='relative flex h-full w-full flex-col'>
          {/* Orange button */}
          <div className='flex justify-center'>
            <div className='w-[35%]'>
              <AspectRatio ratio={137 / 48}>
                <div
                  className='drop-shadow-orange-btn relative flex h-full w-full cursor-pointer items-center justify-center'
                  onClick={handlePlayNow}
                >
                  <MediumOrangeButton className='h-full w-full' />
                  <span className='text-shadow-orange-btn absolute z-20 text-[20px] text-white'>Chơi ngay</span>
                </div>
              </AspectRatio>
            </div>
          </div>

          {/* Bottom 4 buttons → take remaining space */}
          <div className='flex flex-grow items-center justify-center gap-[8%]'>
            {[
              { key: 'guideline', icon: '/home/<USER>', className: 'ml-[5%] h-[90%] w-[80%]' },
              { key: 'mission', icon: '/home/<USER>', className: 'h-[80%] w-[80%]' },
              { key: 'history', icon: '/home/<USER>', className: 'ml-[10%] h-[95%] w-[95%]' },
              { key: 'ranking', icon: '/home/<USER>', className: 'h-[80%] w-[90%]' }
            ].map(({ key, icon, className }) => (
              <SquareIconButton
                key={key}
                icon={icon}
                alt={key}
                className={className}
                onClick={() => {
                  if (key === 'mission') {
                    onOpenDailyMission()
                  } else if (key === 'ranking') {
                    onOpenLeaderboard()
                  } else if (key === 'history') {
                    onOpenHistory()
                  } else {
                    onOpenGuideline()
                  }
                }}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
