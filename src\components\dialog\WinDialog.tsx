// src/components/dialog/WinDialog.tsx
'use client'

import { Dialog, DialogContent } from '@/components/ui/dialog'
import Image from 'next/image'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import RoundIconButton from '@/components/common/RoundIconButton'
import HomeIcon from '@/assets/dialog/home.svg'
import ShareIcon from '@/assets/dialog/share.svg'
import RetryIcon from '@/assets/dialog/retry.svg'
import NextIcon from '@/assets/dialog/next.svg'
import { useForfeitSpin } from '@/hooks/useForfeitSpin'
import { useGameStore } from '@/stores/useGameStore'
import { usePresentUnboxing, UsePresentUnboxingResponse } from '@/hooks/usePresentUnboxing'
import { useCalculateScore, CalculateScoreResponse } from '@/hooks/useCalculateScore'
import { useApiCallGuardStore } from '@/stores/useApiCallGuardStore'
import { useState, useEffect } from 'react'

interface WinDialogProps {
  open: boolean
  onClose: () => void
  onShare: (stage: number) => void // <-- fix here
  stage: number
  onNavigate: (stage: number) => void
  variant?: 'home' | 'game'
  timeCompleted?: number
}

export default function WinDialog({
  open,
  onClose,
  onShare,
  stage,
  onNavigate,
  variant = 'home',
  timeCompleted
}: WinDialogProps) {
  const { gameCampaignId, passedLevels, updateUserLevelProgress, currentLevel } = useGameStore()
  const { mutateAsync: forfeitSpin } = useForfeitSpin()
  const { mutateAsync: presentUnboxing } = usePresentUnboxing()
  const { mutateAsync: calculateScore } = useCalculateScore()
  const { markLevelCalled, hasLevelBeenCalled, startApiCall, finishApiCall } = useApiCallGuardStore()

  const [rewardData, setRewardData] = useState<UsePresentUnboxingResponse | CalculateScoreResponse | null>(null)
  const [isLoadingReward, setIsLoadingReward] = useState(false)

  const backgroundImage = variant === 'game' ? '/dialog/winGameBG.png' : '/dialog/winHomeBG.png'
  const rewardText = variant === 'game' ? 'Chúc mừng bạn đã nhận được' : 'Bạn đã nhận được'
  const ActionIcon = variant === 'game' ? NextIcon : RetryIcon

  // Grab info from store for this stage (fallback data)
  const levelInfo = passedLevels.find((lvl) => lvl.level === stage)
  const fallbackScore = levelInfo?.score ?? 0
  const fallbackRewardIcon = levelInfo?.rewardInfo?.icon ?? '/dialog/logo.png'
  const fallbackRewardTitle = levelInfo?.rewardInfo?.title?.vi ?? 'Voucher đặc biệt từ bTaskee'

  // Use dynamic reward data if available, otherwise fallback to static data
  const score = rewardData?.score ?? fallbackScore
  const rewardIcon = rewardData?.image ?? fallbackRewardIcon
  const rewardTitle = rewardData?.title?.vi ?? fallbackRewardTitle

  // Call appropriate API when dialog opens for game variant (actual win)
  useEffect(() => {
    const fetchReward = async () => {
      if (
        open &&
        variant === 'game' &&
        gameCampaignId &&
        timeCompleted !== undefined &&
        !rewardData &&
        !isLoadingReward
      ) {
        // Check if this level has already been called
        if (hasLevelBeenCalled(stage)) {
          console.log(`Level ${stage} reward already claimed, skipping API call`)
          return
        }

        // Create unique call ID for this level
        const callId = `level-${stage}-reward`

        // Check if we can start the API call (prevents multiple simultaneous calls)
        if (!startApiCall(callId)) {
          console.log(`API call for level ${stage} already in progress`)
          return
        }

        setIsLoadingReward(true)
        try {
          const userId = 'x391d6b4ecadb6625af716fe675cc21cf'

          // Update user level progress in our anti-cheat system
          updateUserLevelProgress(userId, stage, true)

          // Determine if this is a passed level (not the current level)
          const isPassedLevel = stage < currentLevel

          let reward: UsePresentUnboxingResponse | CalculateScoreResponse

          if (isPassedLevel) {
            // Use calculate-score API for passed levels
            reward = await calculateScore({
              userId,
              gameCampaignId,
              levelNumber: stage,
              timeCompleted
            })
          } else {
            // Use present-unboxing API for current level (first time completion)
            reward = await presentUnboxing({
              gameCampaignId,
              from: 'WEBVIEW',
              levelNumber: stage,
              timeCompleted,
              userId
            })
          }

          setRewardData(reward)

          // Mark this level as called to prevent future calls
          markLevelCalled(stage)
        } catch (error) {
          console.error('Failed to fetch reward:', error)
          // Set fallback reward data
          setRewardData({
            image: '/dialog/logo.png',
            rewardKey: 1,
            segment: 1,
            score: 100,
            title: {
              vi: 'Voucher giảm giá 20.000đ của bTaskee',
              en: 'bTaskee 20,000đ discount voucher',
              id: 'Voucher diskon 20.000đ bTaskee',
              ko: 'bTaskee 20,000원 할인 바우처',
              th: 'บาวเชอร์ส่วนลด 20,000 บาท bTaskee'
            }
          })
        } finally {
          setIsLoadingReward(false)
          // Always finish the API call to clean up the guard
          finishApiCall(callId)
        }
      }
    }

    fetchReward()
  }, [
    open,
    variant,
    gameCampaignId,
    timeCompleted,
    stage,
    rewardData,
    isLoadingReward,
    presentUnboxing,
    calculateScore,
    updateUserLevelProgress,
    currentLevel,
    hasLevelBeenCalled,
    startApiCall,
    finishApiCall,
    markLevelCalled
  ])

  const handleActionClick = async () => {
    try {
      if (gameCampaignId) {
        if (variant === 'home') {
          await forfeitSpin({
            action: 'RETRY',
            levelNumber: stage,
            gameCampaignId,
            userId: 'x391d6b4ecadb6625af716fe675cc21cf'
          })
        } else if (variant === 'game') {
          await forfeitSpin({
            action: 'FORFEIT_SPIN',
            levelNumber: stage + 1,
            gameCampaignId,
            userId: 'x391d6b4ecadb6625af716fe675cc21cf'
          })
        }
      }
    } catch (error) {
      console.error('Failed to forfeit spin:', error)
    }

    onClose()
    onNavigate(stage)
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent showCloseButton={false} className='border-none bg-transparent p-0 shadow-none'>
        <AspectRatio ratio={1560 / 2720} className='relative'>
          <Image src={backgroundImage} alt='Win' fill quality={100} />

          {/* Title */}
          <h2 className='font-signwriter absolute top-[26%] left-1/2 w-[35%] -translate-x-1/2 text-center text-[24px] text-[#FFFF70]'>
            Màn {stage}
          </h2>

          {/* Score & Reward */}
          <div className='font-montserrat absolute top-[41%] left-1/2 flex h-[29%] w-[65%] -translate-x-1/2 flex-col text-center'>
            <p className='mt-[5%] text-[10px] font-extrabold text-white'>Điểm</p>
            <p className='score-shadow -translate-y-[25%] text-[33px] font-extrabold text-[#FFFF70]'>{score}</p>
            <p className='my-[4%] text-[12px] font-medium text-white'>{rewardText}</p>
            <div className='w-full'>
              <AspectRatio ratio={996 / 224}>
                <div className='relative flex h-full w-full'>
                  <Image src='/dialog/voucherBG.png' alt='Reward' fill quality={100} />
                  <div className='absolute inset-0 flex'>
                    {/* Left: Reward icon */}
                    <div className='flex h-full w-[22.5%] items-center justify-center'>
                      <AspectRatio ratio={1}>
                        <Image src={rewardIcon} alt='Reward Icon' fill className='p-[15%]' quality={100} />
                      </AspectRatio>
                    </div>
                    {/* Right: Reward title */}
                    <div className='flex h-full w-[77.5%] items-center bg-gradient-to-b from-[#FFFF70] to-[#FF8228] to-50% bg-clip-text px-[4%] text-start text-[14px] font-bold text-transparent'>
                      {rewardTitle}
                    </div>
                  </div>
                </div>
              </AspectRatio>
            </div>
          </div>

          {/* Round Buttons */}
          <div className='absolute top-[69.5%] left-1/2 flex h-[7.5%] w-[47.5%] -translate-x-1/2 items-center justify-between'>
            <RoundIconButton
              className='w-[10.6dvw]'
              icon={<ShareIcon className='z-20 h-[50%] w-[50%]' />}
              onClick={() => {
                onClose()
                onShare(stage) // pass the current stage
              }}
            />

            <RoundIconButton
              className='w-[10.6dvw]'
              icon={<HomeIcon className='z-20 h-[50%] w-[50%]' />}
              onClick={onClose}
            />
            <RoundIconButton
              className='w-[10.6dvw]'
              icon={<ActionIcon className='z-20 h-[50%] w-[50%]' />}
              onClick={handleActionClick}
            />
          </div>
        </AspectRatio>
      </DialogContent>
    </Dialog>
  )
}
