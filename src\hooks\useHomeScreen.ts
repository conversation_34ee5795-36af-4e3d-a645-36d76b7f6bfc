// src/hooks/useHomeScreen.ts
import { useEffect, useRef, useState } from 'react'
import { useAudioStore } from '@/stores/useAudioStore'
import { useGameStore } from '@/stores/useGameStore'
import { useCheckIn } from '@/hooks/useCheckIn'
import { useAlertPopup } from '@/hooks/useAlertPopup'
import { useAlertPopupStore } from '@/stores/useAlertPopupStore'
import { useForfeitSpin } from '@/hooks/useForfeitSpin'
import { useGameCampaign } from '@/hooks/useGameCampaign'

type DialogType = 'checkin' | 'task'

export function useHomeScreen(onStageClick: (stage: number) => void) {
  const viewportRef = useRef<HTMLDivElement | null>(null)
  const stageRefs = useRef<Record<number, HTMLDivElement | null>>({})
  const { hasLoggedInToday, setHasLoggedInToday, currentLevel, turns, gameCampaignId, setGameCampaign } = useGameStore()
  const [showOutOfTurns, setShowOutOfTurns] = useState(false)
  const [dialogQueue, setDialogQueue] = useState<{ type: DialogType; title: string; rewardCount: number }[]>([])
  const [currentDialog, setCurrentDialog] = useState<{ type: DialogType; title: string; rewardCount: number } | null>(
    null
  )
  const [winStage, setWinStage] = useState<number | null>(null)
  const { isMuted: muted, toggleMute } = useAudioStore()
  const { mutateAsync: fetchAlertPopup } = useAlertPopup()
  const { setPopup } = useAlertPopupStore()
  const { mutateAsync: checkIn } = useCheckIn()
  const { mutateAsync: forfeitSpin } = useForfeitSpin()
  const { mutateAsync: refreshGameCampaign } = useGameCampaign()

  const getVariant = (stage: number) => {
    if (stage < currentLevel) return 'completed'
    if (stage === currentLevel) return 'current'
    return 'unreached'
  }

  const handleStageClick = async (stage: number) => {
    const variant = getVariant(stage)
    if (variant === 'unreached') return
    if (turns <= 0) {
      setShowOutOfTurns(true)
      return
    }
    if (stage < currentLevel) {
      setWinStage(stage)
      return
    }

    // For current level, call forfeit-spin API and navigate to game
    if (stage === currentLevel) {
      try {
        if (gameCampaignId) {
          await forfeitSpin({
            action: 'FORFEIT_SPIN',
            levelNumber: stage,
            gameCampaignId,
            userId: 'x391d6b4ecadb6625af716fe675cc21cf'
          })

          // Refresh game campaign data before navigating to game
          const data = await refreshGameCampaign({
            userId: 'x391d6b4ecadb6625af716fe675cc21cf',
            appVersion: '1.0.0',
            from: 'web'
          })
          setGameCampaign(data)
        }
      } catch (error) {
        console.error('Failed to forfeit spin or refresh game campaign:', error)
        // Continue to game even if API call fails
      }
    }

    onStageClick(stage)
  }

  useEffect(() => {
    const container = viewportRef.current
    const currentEl = stageRefs.current[currentLevel]
    if (!container || !currentEl) return

    const id = setTimeout(() => {
      const containerRect = container.getBoundingClientRect()
      const elRect = currentEl.getBoundingClientRect()
      const offset = elRect.top - containerRect.top - container.clientHeight * 0.7 + elRect.height / 2

      container.scrollTop = container.scrollTop + offset
    }, 100)

    return () => clearTimeout(id)
  }, [currentLevel])

  const hasCheckInRunRef = useRef(false)

  useEffect(() => {
    const runDialogs = async () => {
      if (hasCheckInRunRef.current) return
      hasCheckInRunRef.current = true

      const queue: { type: DialogType; title: string; rewardCount: number }[] = []

      if (!hasLoggedInToday && gameCampaignId) {
        try {
          const res = await checkIn({
            userId: 'x391d6b4ecadb6625af716fe675cc21cf',
            gameCampaignId
          })
          setHasLoggedInToday(true)
          queue.push({
            type: 'checkin',
            title: 'Điểm danh thành công!',
            rewardCount: res.rewards
          })
        } catch (err) {
          console.error('Check-in failed:', err)
        }
      }

      if (gameCampaignId) {
        try {
          const res = await fetchAlertPopup({
            userId: 'x391d6b4ecadb6625af716fe675cc21cf',
            gameCampaignId
          })
          if (res.length > 0 && res[0].completeMissions.length > 0) {
            setPopup(res[0])
            queue.push({
              type: 'task',
              title: 'Hoàn thành nhiệm vụ!',
              rewardCount: res[0].totalSpin
            })
          }
        } catch (err) {
          console.error('getAlertPopup failed:', err)
        }
      }

      setDialogQueue(queue)
      if (queue.length > 0) setCurrentDialog(queue[0])
    }

    runDialogs()
  }, [])

  const handleCloseDialog = () => {
    if (dialogQueue.length <= 1) {
      setCurrentDialog(null)
      setDialogQueue([])
    } else {
      const [, ...rest] = dialogQueue
      setDialogQueue(rest)
      setCurrentDialog(rest[0])
    }
  }

  return {
    viewportRef,
    stageRefs,
    muted,
    toggleMute,
    showOutOfTurns,
    setShowOutOfTurns,
    currentDialog,
    handleCloseDialog,
    winStage,
    setWinStage,
    getVariant,
    handleStageClick
  }
}
