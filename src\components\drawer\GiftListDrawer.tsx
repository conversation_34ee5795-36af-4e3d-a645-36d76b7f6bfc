'use client'

import { <PERSON>er, <PERSON><PERSON><PERSON>ontent, Draw<PERSON><PERSON>eader, Drawer<PERSON><PERSON><PERSON>, DrawerClose, DrawerFooter } from '@/components/ui/drawer'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import { ScrollArea } from '@/components/ui/scroll-area'
import Image from 'next/image'
import LargeOrangeButton from '@/assets/drawer/largeOrangeButton.svg'
import VoucherGiftBG from '@/assets/drawer/voucherGiftBG.svg'
import EmptyReward from '@/assets/drawer/empy_reward.svg'

interface Voucher {
  avatar: string
  description: string
}

interface GiftListDrawerProps {
  open: boolean
  onClose: () => void
}

// Move vouchers inside the component or declare as const
const vouchers: Voucher[] = [
  { avatar: '/dialog/logo.png', description: 'Voucher giảm giá 20.000đ của bTaskee' },
  { avatar: '/dialog/logo.png', description: 'Voucher giảm giá 20.000đ của bTaskee' }
]

const GiftListDrawer: React.FC<GiftListDrawerProps> = ({ open, onClose }) => {
  return (
    <Drawer open={open} onOpenChange={onClose}>
      <DrawerContent className='flex h-[97.5dvh] flex-col overflow-hidden'>
        {/* Fixed header */}
        <DrawerHeader className='h-[18.5%]'>
          <DrawerTitle className='font-bdstreet text-border-orange text-[32px]'>Danh sách quà</DrawerTitle>
        </DrawerHeader>

        {/* Scrollable content */}
        <div className='font-montserrat flex-1 overflow-hidden'>
          {vouchers.length === 0 ? (
            <div className='flex h-full flex-1 flex-col items-center justify-center'>
              <div className='flex w-[50%] flex-col items-center space-y-[20%]'>
                <AspectRatio ratio={1}>
                  <EmptyReward />
                </AspectRatio>
                <p className='text-center text-[14px] font-medium text-white'>Danh sách trống</p>
              </div>
            </div>
          ) : (
            <>
              <div className='h-[12%] px-[5%] pt-[2%]'>
                <p className='text-center text-[14px] font-medium text-white'>
                  Quà của bạn sẽ được lưu tại <span className='font-bold'>“Ưu đãi của tôi”</span> trên ứng dụng bTaskee
                </p>
              </div>

              <ScrollArea className='h-[88%] w-full pt-[3%]'>
                <div className='flex flex-col items-center space-y-[3%]'>
                  {vouchers.map((voucher, idx) => (
                    <div key={idx} className='w-[91.7%]'>
                      <AspectRatio ratio={360 / 82} className='relative w-full'>
                        <VoucherGiftBG className='absolute inset-0 h-full w-full' />
                        <div className='absolute inset-0 flex'>
                          {/* Left avatar */}
                          <div className='flex h-full w-[22.5%] items-center justify-center'>
                            <AspectRatio ratio={1}>
                              <Image src={voucher.avatar} alt='Voucher Avatar' fill className='p-[15%]' quality={100} />
                            </AspectRatio>
                          </div>
                          {/* Right description */}
                          <div className='flex h-full w-[77.5%] items-center px-[4%] text-start text-[14px] font-medium text-white'>
                            {voucher.description}
                          </div>
                        </div>
                      </AspectRatio>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </>
          )}
        </div>

        {/* Footer */}
        <DrawerFooter className='flex h-[11%] items-center justify-center pb-[5%]'>
          <DrawerClose className='w-full'>
            <AspectRatio ratio={326 / 48} className='relative mx-auto w-[90%]'>
              <LargeOrangeButton className='absolute inset-0 h-full w-full' />
              <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] text-white'>
                Đóng
              </span>
            </AspectRatio>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}

export default GiftListDrawer
