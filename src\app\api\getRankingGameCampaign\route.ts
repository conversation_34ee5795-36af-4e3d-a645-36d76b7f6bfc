import { NextResponse } from 'next/server'
import axios, { AxiosError } from 'axios'

interface RankingRequest {
  [key: string]: unknown
}

interface RankingResponse {
  [key: string]: unknown
}

export async function POST(req: Request) {
  const body: RankingRequest = await req.json()

  try {
    const { data } = await axios.post<RankingResponse>(
      'http://192.168.88.71/api/v5/api-asker-vn/get-ranking-game-campaign',
      body,
      {
        headers: {
          accessKey: '****************************************************************',
          Authorization:
            'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJraWQiOiJqd3VRcWwzOFJIYzFyTHltY2M4RmpzZ2dBV0w3ck0yciIsInVpZCI6IngzOTFkNmI0ZWNhZGI2NjI1YWY3MTZmZTY3NWNjMjFjZiIsImV4cCI6MTc1OTEyMTYyOX0.PGISaysSJoK5P0EojUWbcI4NurLtmNUorJmpKkSvFvA',
          'Content-Type': 'application/json'
        }
      }
    )

    return NextResponse.json(data)
  } catch (err) {
    const error = err as AxiosError
    return NextResponse.json({ error: error.message }, { status: error.response?.status ?? 500 })
  }
}
