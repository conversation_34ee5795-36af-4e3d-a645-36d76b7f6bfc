// src/lib/userLevelStorage.ts
// In-memory storage for user level tracking and anti-cheat verification

interface UserLevelData {
  userId: string
  currentLevel: number
  passedLevels: number[]
  lastUpdated: Date
}

// In-memory storage - in production, this should be replaced with a proper database
const userLevelStorage = new Map<string, UserLevelData>()

export class UserLevelManager {
  /**
   * Update user's current level and add to passed levels if completing a level
   */
  static updateUserLevel(userId: string, level: number, isCompleting: boolean = false): void {
    const existing = userLevelStorage.get(userId)
    
    if (existing) {
      // Update existing user data
      existing.currentLevel = Math.max(existing.currentLevel, level)
      existing.lastUpdated = new Date()
      
      if (isCompleting && !existing.passedLevels.includes(level)) {
        existing.passedLevels.push(level)
        existing.passedLevels.sort((a, b) => a - b)
      }
    } else {
      // Create new user data
      const newUserData: UserLevelData = {
        userId,
        currentLevel: level,
        passedLevels: isCompleting ? [level] : [],
        lastUpdated: new Date()
      }
      userLevelStorage.set(userId, newUserData)
    }
  }

  /**
   * Check if user exists and is currently on the specified level
   */
  static isUserOnCurrentLevel(userId: string, level: number): boolean {
    const userData = userLevelStorage.get(userId)
    if (!userData) {
      return false
    }
    
    return userData.currentLevel === level
  }

  /**
   * Check if user exists and has passed the specified level
   */
  static hasUserPassedLevel(userId: string, level: number): boolean {
    const userData = userLevelStorage.get(userId)
    if (!userData) {
      return false
    }
    
    return userData.passedLevels.includes(level)
  }

  /**
   * Get user's current level data
   */
  static getUserData(userId: string): UserLevelData | null {
    return userLevelStorage.get(userId) || null
  }

  /**
   * Initialize user data from game campaign response
   */
  static initializeUserFromGameCampaign(userId: string, furthestLevel: number, passedLevels: number[]): void {
    const existing = userLevelStorage.get(userId)
    
    if (!existing) {
      const newUserData: UserLevelData = {
        userId,
        currentLevel: furthestLevel,
        passedLevels: [...passedLevels].sort((a, b) => a - b),
        lastUpdated: new Date()
      }
      userLevelStorage.set(userId, newUserData)
    } else {
      // Update with the latest data from backend
      existing.currentLevel = Math.max(existing.currentLevel, furthestLevel)
      existing.passedLevels = [...new Set([...existing.passedLevels, ...passedLevels])].sort((a, b) => a - b)
      existing.lastUpdated = new Date()
    }
  }

  /**
   * Debug method to get all stored user data
   */
  static getAllUserData(): UserLevelData[] {
    return Array.from(userLevelStorage.values())
  }

  /**
   * Clear all user data (for testing purposes)
   */
  static clearAllData(): void {
    userLevelStorage.clear()
  }
}
